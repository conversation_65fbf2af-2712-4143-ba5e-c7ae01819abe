#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻源测试脚本
用于诊断新闻获取问题
"""

import requests
import feedparser
from datetime import datetime, timedelta
import time

def test_rss_source(name, url):
    """测试单个RSS源"""
    print(f"\n{'='*50}")
    print(f"测试新闻源: {name}")
    print(f"URL: {url}")
    print(f"{'='*50}")
    
    try:
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        
        # 发送请求
        print("发送HTTP请求...")
        start_time = time.time()
        response = requests.get(url, headers=headers, timeout=15)
        request_time = time.time() - start_time
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"请求耗时: {request_time:.2f}秒")
        print(f"响应大小: {len(response.content)} 字节")
        
        if response.status_code != 200:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
        
        # 解析RSS
        print("解析RSS内容...")
        feed = feedparser.parse(response.content)
        
        if not feed.entries:
            print("❌ RSS源没有返回任何文章")
            return False
        
        print(f"✅ 成功获取 {len(feed.entries)} 篇文章")
        
        # 分析最新文章
        print("\n最新文章信息:")
        for i, entry in enumerate(feed.entries[:3]):
            print(f"\n文章 {i+1}:")
            print(f"  标题: {entry.title}")
            
            # 处理发布时间
            pub_time = None
            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                pub_time = datetime(*entry.published_parsed[:6])
                print(f"  发布时间: {pub_time}")
            elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                pub_time = datetime(*entry.updated_parsed[:6])
                print(f"  更新时间: {pub_time}")
            else:
                print(f"  发布时间: 未知")
            
            if pub_time:
                time_diff = datetime.now() - pub_time
                print(f"  时间差: {time_diff}")
                
                if time_diff.days > 1:
                    print(f"  ⚠️  文章较旧 (超过1天)")
                elif time_diff.seconds > 3600:
                    print(f"  ⚠️  文章较旧 (超过1小时)")
                else:
                    print(f"  ✅ 文章较新")
        
        return True
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
        return False
    except Exception as e:
        print(f"❌ 解析错误: {e}")
        return False

def main():
    """主函数"""
    print("AI文章撰写工具 - 新闻源诊断")
    print(f"测试时间: {datetime.now()}")
    
    # 测试的新闻源
    sources = [
        ('新浪新闻-要闻', 'https://rss.sina.com.cn/news/china/focus15.xml'),
        ('人民网-时政', 'http://www.people.com.cn/rss/politics.xml'),
        ('新华网-时政', 'http://www.xinhuanet.com/politics/news_politics.xml'),
        ('中国新闻网', 'https://www.chinanews.com.cn/rss/scroll-news.xml'),
        ('光明网', 'http://www.gmw.cn/rss/news.xml'),
    ]
    
    successful_sources = 0
    total_sources = len(sources)
    
    for name, url in sources:
        if test_rss_source(name, url):
            successful_sources += 1
        time.sleep(1)  # 避免请求过快
    
    print(f"\n{'='*50}")
    print("测试总结")
    print(f"{'='*50}")
    print(f"总计测试: {total_sources} 个新闻源")
    print(f"成功: {successful_sources} 个")
    print(f"失败: {total_sources - successful_sources} 个")
    print(f"成功率: {successful_sources/total_sources*100:.1f}%")
    
    if successful_sources == 0:
        print("\n❌ 所有新闻源都无法访问，可能的原因:")
        print("1. 网络连接问题")
        print("2. 防火墙阻止")
        print("3. RSS源地址已失效")
        print("4. 需要配置代理")
    elif successful_sources < total_sources:
        print(f"\n⚠️  部分新闻源无法访问，建议:")
        print("1. 检查失效的RSS源地址")
        print("2. 更新新闻源配置")
        print("3. 增加备用新闻源")
    else:
        print("\n✅ 所有新闻源都可以正常访问")

if __name__ == "__main__":
    main()
