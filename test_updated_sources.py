#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的新闻源
"""

import requests
import feedparser
from datetime import datetime
import time

def test_updated_sources():
    """测试更新后的新闻源"""
    
    # 更新后的新闻源
    sources = [
        ('中国新闻网-滚动', 'https://www.chinanews.com.cn/rss/scroll-news.xml'),
        ('新华网-时政', 'http://www.xinhuanet.com/politics/news_politics.xml'),
        ('人民网-时政', 'http://www.people.com.cn/rss/politics.xml'),
        ('网易新闻', 'https://news.163.com/special/00011K6L/rss_newstop.xml'),
        ('搜狐新闻', 'https://news.sohu.com/rss/news.xml'),
        ('腾讯新闻', 'https://news.qq.com/newsgn/rss_newsgn.xml'),
        ('凤凰网', 'https://news.ifeng.com/rss/index.xml'),
    ]
    
    print("测试更新后的新闻源配置")
    print(f"测试时间: {datetime.now()}")
    print("="*60)
    
    successful = 0
    recent_news_count = 0
    
    for name, url in sources:
        print(f"\n测试: {name}")
        print(f"URL: {url}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                feed = feedparser.parse(response.content)
                
                if feed.entries:
                    print(f"✅ 成功 - 获取到 {len(feed.entries)} 篇文章")
                    successful += 1
                    
                    # 检查最新文章时间
                    latest = feed.entries[0]
                    if hasattr(latest, 'published_parsed') and latest.published_parsed:
                        pub_time = datetime(*latest.published_parsed[:6])
                        time_diff = datetime.now() - pub_time
                        
                        print(f"   最新文章: {latest.title[:50]}...")
                        print(f"   发布时间: {pub_time}")
                        
                        if time_diff.days == 0 and time_diff.seconds < 3600:
                            print(f"   ✅ 很新 (1小时内)")
                            recent_news_count += 1
                        elif time_diff.days == 0:
                            print(f"   ✅ 较新 (今天)")
                            recent_news_count += 1
                        elif time_diff.days <= 1:
                            print(f"   ⚠️  一般 (1天内)")
                        else:
                            print(f"   ❌ 较旧 ({time_diff.days}天前)")
                    else:
                        print(f"   ⚠️  无时间信息")
                else:
                    print(f"❌ 无文章内容")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        time.sleep(1)
    
    print("\n" + "="*60)
    print("测试总结:")
    print(f"总源数: {len(sources)}")
    print(f"可用源: {successful}")
    print(f"有最新内容的源: {recent_news_count}")
    print(f"成功率: {successful/len(sources)*100:.1f}%")
    
    if recent_news_count > 0:
        print(f"✅ 找到 {recent_news_count} 个有最新内容的新闻源")
    else:
        print("❌ 没有找到最新内容的新闻源")

if __name__ == "__main__":
    test_updated_sources()
